# nestjsx/crud overview

## What it is
- Auto-generates REST controllers with rich query semantics using minimal config.
- Monorepo with three key packages:
  - @nestjsx/crud: Crud() decorator, route factory, interceptors, global config.
  - @nestjsx/crud-request: RequestQueryParser/RequestQueryBuilder; operators, joins, pagination.
  - @nestjsx/crud-typeorm: TypeOrmCrudService that maps parsed requests into TypeORM QueryBuilder.

## Core flow
1) CrudConfigService.load(): set global defaults.
2) @Crud({...}): stores metadata and invokes CrudRoutesFactory to synthesize routes and swagger + interceptors.
3) CrudRequestInterceptor: parses query (?filter, ?sort, ?join, ?page/limit, ?s search, params, soft-delete) -> ParsedRequestParams.
4) TypeOrmCrudService: converts parsed params to TypeORM QueryBuilder (joins, where, select, order, paginate, cache).
5) CrudResponseInterceptor: serializes data to DTOs or pagination envelopes per serialize options.

## Key subsystems
- CrudRoutesFactory: builds route methods; respects routes.only/exclude, params, DTO groups; sets OpenAPI.
- RequestQueryParser: validates/normalizes query; operators $eq,$ne,$gt,$lt,$gte,$lte,$starts,$ends,$cont,$excl,$in,$notin,$isnull,$notnull,$between (+ case-insensitive variants).
- TypeOrmCrudService: safe SQL mapping with params; joins (eager/nested), fields allow/exclude, soft delete, pagination, cache.
- Interceptors: CrudRequestInterceptor, CrudResponseInterceptor.
- Swagger: auto query params, path params, response models; dynamic GetManyXResponseDto.
- Validation: DTOs and bulk create DTOs via class-validator/transformer.

## Design patterns
- Decorator (Crud, Feature, Action)
- Factory (CrudRoutesFactory)
- Builder (RequestQueryBuilder; TypeORM QueryBuilder construction)
- Template Method (CrudService -> TypeOrmCrudService)
- Strategy (swap ORM service; custom validation/serialization)
- Facade (CrudOptions)
- AOP via interceptors

## Boilerplate guidance
- Repo layout: packages/{crud, crud-request, crud-typeorm} or core/crud/* in single app.
- Global config via CrudConfigService: query/ routes/ params/ serialize.
- @Crud decorator should synthesize CRUD route methods, bind interceptors, and set Swagger.
- Request parsing: RequestQueryParser + RequestQueryBuilder for clients.
- ORM service abstraction: CrudService<T>; TypeOrmCrudService<T> mapping.
- Interceptors for parsing/serialization.
- Security: validation pipes, SQLi guard, limits, soft-delete handling, ACL hooks.
- Observability: OpenAPI, normalized errors, logging/metrics hooks.
- Caching: TTL, Redis; ?cache=0 busting.
- Extensibility: @Override('getManyBase'), adapters for other ORMs, hooks.
- Foundation modules: Auth, audit log, global exception filter, Redis, Swagger, health checks.
- Testing: parser, factory, service SQL mapping; e2e for routes and responses.
- CI/CD: lint, typecheck, unit, e2e; versioned publishing.

## Minimal snippets
- Global config/service, Crud decorator to factory, interceptor registration, operator mapping examples.

References: https://github.com/nestjsx/crud