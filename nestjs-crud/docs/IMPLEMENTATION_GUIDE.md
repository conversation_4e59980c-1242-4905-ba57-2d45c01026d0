# 🚀 NestJS-CRUD Enterprise Implementation Guide

> **Practical guide for implementing enterprise-grade CRUD operations using NestJS-CRUD with design patterns, algorithms, and best practices**

## 🎯 **Quick Start Implementation**

### **1. Project Setup with Enterprise Structure**

```bash
# Create new NestJS project with enterprise structure
npm i -g @nestjs/cli
nest new enterprise-crud-app

# Install NestJS-CRUD packages
npm install @nestjsx/crud @nestjsx/crud-typeorm @nestjsx/crud-request
npm install class-transformer class-validator
npm install typeorm @nestjs/typeorm pg

# Install additional enterprise packages
npm install @nestjs/swagger @nestjs/jwt @nestjs/passport
npm install helmet compression rate-limiter-flexible
```

### **2. Enterprise Project Structure**

```
src/
├── 🏗️ core/                    # Core infrastructure
│   ├── database/               # Database configuration
│   ├── security/               # Security middleware
│   ├── monitoring/             # Metrics and logging
│   └── cache/                  # Caching strategies
├── 🧠 domain/                  # Domain layer (DDD)
│   ├── entities/               # Domain entities
│   ├── value-objects/          # Value objects
│   ├── events/                 # Domain events
│   └── services/               # Domain services
├── ⚡ application/              # Application layer
│   ├── services/               # Application services
│   ├── dto/                    # Data transfer objects
│   ├── interfaces/             # Service interfaces
│   └── use-cases/              # Use case implementations
├── 🌐 presentation/            # Presentation layer
│   ├── controllers/            # HTTP controllers
│   ├── guards/                 # Authentication guards
│   ├── interceptors/           # Request/response interceptors
│   └── filters/                # Exception filters
└── 🏗️ infrastructure/          # Infrastructure layer
    ├── repositories/           # Data repositories
    ├── external/               # External service clients
    └── config/                 # Configuration
```

## 🏛️ **Enterprise Entity Implementation**

### **Domain Entity with Design Patterns**

```typescript
// domain/entities/user.entity.ts
import { Entity, PrimaryGeneratedColumn, Column, CreateDateColumn, UpdateDateColumn, DeleteDateColumn } from 'typeorm';
import { IsEmail, IsString, MinLength, MaxLength } from 'class-validator';
import { Exclude, Transform } from 'class-transformer';

@Entity('users')
export class User {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ unique: true })
  @IsEmail()
  email: string;

  @Column()
  @IsString()
  @MinLength(2)
  @MaxLength(50)
  firstName: string;

  @Column()
  @IsString()
  @MinLength(2)
  @MaxLength(50)
  lastName: string;

  @Column()
  @Exclude() // Security: Exclude from serialization
  @MinLength(8)
  password: string;

  @Column({ default: true })
  isActive: boolean;

  @Column({ nullable: true })
  lastLoginAt: Date;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;

  @DeleteDateColumn()
  deletedAt: Date;

  // Domain method - Business logic encapsulation
  public activate(): void {
    this.isActive = true;
  }

  public deactivate(): void {
    this.isActive = false;
  }

  // Value object pattern for full name
  @Transform(({ obj }) => `${obj.firstName} ${obj.lastName}`)
  get fullName(): string {
    return `${this.firstName} ${this.lastName}`;
  }

  // Factory method pattern
  public static create(email: string, firstName: string, lastName: string, password: string): User {
    const user = new User();
    user.email = email;
    user.firstName = firstName;
    user.lastName = lastName;
    user.password = password;
    user.isActive = true;
    return user;
  }
}
```

## 🔧 **Enterprise Service Implementation**

### **SOLID Principles Applied**

```typescript
// application/services/users.service.ts
import { Injectable, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { TypeOrmCrudService } from '@nestjsx/crud-typeorm';
import { CrudRequest } from '@nestjsx/crud';
import { User } from '../../domain/entities/user.entity';
import { CreateUserDto, UpdateUserDto } from '../dto/user.dto';
import { IUserService } from '../interfaces/user-service.interface';
import { ICacheService } from '../../core/cache/cache-service.interface';
import { IEventBus } from '../../core/events/event-bus.interface';
import { UserCreatedEvent, UserUpdatedEvent } from '../../domain/events/user.events';

// Single Responsibility Principle: Handles only User CRUD operations
@Injectable()
export class UsersService extends TypeOrmCrudService<User> implements IUserService {
  private readonly logger = new Logger(UsersService.name);

  constructor(
    @InjectRepository(User)
    private readonly userRepository: Repository<User>,
    
    // Dependency Inversion Principle: Depend on abstractions
    private readonly cacheService: ICacheService,
    private readonly eventBus: IEventBus,
  ) {
    super(userRepository);
  }

  // Template Method Pattern: Override base behavior
  async createOne(req: CrudRequest, dto: CreateUserDto): Promise<User> {
    this.logger.log(`Creating user with email: ${dto.email}`);
    
    try {
      // Business logic validation
      await this.validateUniqueEmail(dto.email);
      
      // Hash password (security best practice)
      const hashedPassword = await this.hashPassword(dto.password);
      
      // Factory method pattern
      const user = User.create(dto.email, dto.firstName, dto.lastName, hashedPassword);
      
      // Save to database
      const savedUser = await super.createOne(req, user);
      
      // Cache the new user (performance optimization)
      await this.cacheService.set(`user:${savedUser.id}`, savedUser, 3600);
      
      // Publish domain event (event-driven architecture)
      await this.eventBus.publish(new UserCreatedEvent(savedUser));
      
      this.logger.log(`User created successfully: ${savedUser.id}`);
      return savedUser;
      
    } catch (error) {
      this.logger.error(`Failed to create user: ${error.message}`, error.stack);
      throw error;
    }
  }

  // Open/Closed Principle: Extend functionality without modifying base class
  async updateOne(req: CrudRequest, dto: UpdateUserDto): Promise<User> {
    const userId = req.parsed.paramsFilter[0].value;
    
    try {
      // Get existing user from cache first (performance optimization)
      let existingUser = await this.cacheService.get(`user:${userId}`);
      
      if (!existingUser) {
        existingUser = await this.getOneOrFail(req);
        await this.cacheService.set(`user:${userId}`, existingUser, 3600);
      }
      
      // Business logic: Update last login if password changed
      if (dto.password) {
        dto.password = await this.hashPassword(dto.password);
        dto.lastLoginAt = new Date();
      }
      
      const updatedUser = await super.updateOne(req, dto);
      
      // Update cache
      await this.cacheService.set(`user:${updatedUser.id}`, updatedUser, 3600);
      
      // Publish domain event
      await this.eventBus.publish(new UserUpdatedEvent(updatedUser, existingUser));
      
      return updatedUser;
      
    } catch (error) {
      this.logger.error(`Failed to update user ${userId}: ${error.message}`, error.stack);
      throw error;
    }
  }

  // Interface Segregation Principle: Specific methods for specific needs
  async findByEmail(email: string): Promise<User | null> {
    const cacheKey = `user:email:${email}`;
    
    // Try cache first (O(1) lookup)
    let user = await this.cacheService.get(cacheKey);
    
    if (!user) {
      // Database lookup (O(log n) with index)
      user = await this.userRepository.findOne({ where: { email } });
      
      if (user) {
        await this.cacheService.set(cacheKey, user, 3600);
      }
    }
    
    return user;
  }

  async activateUser(userId: string): Promise<User> {
    const user = await this.userRepository.findOne({ where: { id: userId } });
    
    if (!user) {
      throw new Error('User not found');
    }
    
    // Domain method encapsulates business logic
    user.activate();
    
    const savedUser = await this.userRepository.save(user);
    
    // Update cache
    await this.cacheService.set(`user:${userId}`, savedUser, 3600);
    
    return savedUser;
  }

  // Private methods for internal logic
  private async validateUniqueEmail(email: string): Promise<void> {
    const existingUser = await this.findByEmail(email);
    
    if (existingUser) {
      throw new Error('Email already exists');
    }
  }

  private async hashPassword(password: string): Promise<string> {
    const bcrypt = await import('bcrypt');
    return bcrypt.hash(password, 12);
  }
}
```

### **Interface Definition (ISP)**

```typescript
// application/interfaces/user-service.interface.ts
import { CrudRequest } from '@nestjsx/crud';
import { User } from '../../domain/entities/user.entity';
import { CreateUserDto, UpdateUserDto } from '../dto/user.dto';

// Interface Segregation Principle: Separate interfaces for different concerns
export interface IUserReader {
  getMany(req: CrudRequest): Promise<User[]>;
  getOne(req: CrudRequest): Promise<User>;
  findByEmail(email: string): Promise<User | null>;
}

export interface IUserWriter {
  createOne(req: CrudRequest, dto: CreateUserDto): Promise<User>;
  updateOne(req: CrudRequest, dto: UpdateUserDto): Promise<User>;
  deleteOne(req: CrudRequest): Promise<void>;
}

export interface IUserService extends IUserReader, IUserWriter {
  activateUser(userId: string): Promise<User>;
  deactivateUser(userId: string): Promise<User>;
}
```

## 🌐 **Enterprise Controller Implementation**

### **Clean Architecture Controller**

```typescript
// presentation/controllers/users.controller.ts
import { 
  Controller, 
  UseGuards, 
  UseInterceptors, 
  Logger,
  Get,
  Post,
  Patch,
  Delete,
  Param,
  Body,
  Query
} from '@nestjs/common';
import { 
  Crud, 
  CrudController, 
  CrudRequest, 
  ParsedRequest,
  CreateManyDto,
  Override
} from '@nestjsx/crud';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth } from '@nestjs/swagger';
import { User } from '../../domain/entities/user.entity';
import { UsersService } from '../../application/services/users.service';
import { CreateUserDto, UpdateUserDto, UserResponseDto } from '../../application/dto/user.dto';
import { JwtAuthGuard } from '../../core/security/jwt-auth.guard';
import { RolesGuard } from '../../core/security/roles.guard';
import { Roles } from '../../core/security/roles.decorator';
import { LoggingInterceptor } from '../../core/monitoring/logging.interceptor';
import { CacheInterceptor } from '../../core/cache/cache.interceptor';
import { RateLimitGuard } from '../../core/security/rate-limit.guard';

@ApiTags('Users')
@ApiBearerAuth()
@Controller('users')
@UseGuards(JwtAuthGuard, RolesGuard, RateLimitGuard)
@UseInterceptors(LoggingInterceptor, CacheInterceptor)
@Crud({
  model: {
    type: User,
  },
  dto: {
    create: CreateUserDto,
    update: UpdateUserDto,
    replace: UpdateUserDto,
  },
  serialize: {
    get: UserResponseDto,
    getMany: UserResponseDto,
    create: UserResponseDto,
    update: UserResponseDto,
    replace: UserResponseDto,
  },
  query: {
    // Performance optimization: Limit max results
    maxLimit: 100,
    // Security: Exclude sensitive fields
    exclude: ['password'],
    // Caching strategy
    cache: 2000,
    // Soft delete support
    softDelete: true,
    // Join configuration for related entities
    join: {
      profile: { eager: true },
      roles: { eager: false, allow: ['name'] },
    },
    // Sorting configuration
    sort: [
      { field: 'createdAt', order: 'DESC' },
    ],
  },
  routes: {
    // Security: Restrict certain operations
    only: ['getManyBase', 'getOneBase', 'createOneBase', 'updateOneBase'],
    getManyBase: {
      decorators: [
        Roles('admin', 'user'),
      ],
    },
    getOneBase: {
      decorators: [
        Roles('admin', 'user'),
      ],
    },
    createOneBase: {
      decorators: [
        Roles('admin'),
      ],
    },
    updateOneBase: {
      decorators: [
        Roles('admin', 'user'),
      ],
    },
  },
  params: {
    id: {
      field: 'id',
      type: 'uuid',
      primary: true,
    },
  },
})
export class UsersController implements CrudController<User> {
  private readonly logger = new Logger(UsersController.name);

  constructor(public service: UsersService) {}

  // Override base method to add custom logic
  @Override()
  @ApiOperation({ summary: 'Get many users with advanced filtering' })
  @ApiResponse({ status: 200, description: 'Users retrieved successfully', type: [UserResponseDto] })
  async getMany(@ParsedRequest() req: CrudRequest) {
    this.logger.log(`Getting users with filters: ${JSON.stringify(req.parsed.filter)}`);
    
    // Custom business logic before calling service
    const result = await this.service.getMany(req);
    
    // Custom response transformation
    if (Array.isArray(result)) {
      return {
        data: result,
        count: result.length,
        total: result.length,
        page: 1,
        pageCount: 1,
      };
    }
    
    return result;
  }

  @Override()
  @ApiOperation({ summary: 'Create a new user' })
  @ApiResponse({ status: 201, description: 'User created successfully', type: UserResponseDto })
  async createOne(@ParsedRequest() req: CrudRequest, @Body() dto: CreateUserDto) {
    this.logger.log(`Creating user: ${dto.email}`);
    
    // Additional validation or business logic
    if (!dto.email || !dto.password) {
      throw new Error('Email and password are required');
    }
    
    return this.service.createOne(req, dto);
  }

  // Custom endpoint for user activation
  @Post(':id/activate')
  @Roles('admin')
  @ApiOperation({ summary: 'Activate a user' })
  @ApiResponse({ status: 200, description: 'User activated successfully', type: UserResponseDto })
  async activateUser(@Param('id') id: string): Promise<User> {
    this.logger.log(`Activating user: ${id}`);
    return this.service.activateUser(id);
  }

  // Custom endpoint for user deactivation
  @Post(':id/deactivate')
  @Roles('admin')
  @ApiOperation({ summary: 'Deactivate a user' })
  @ApiResponse({ status: 200, description: 'User deactivated successfully', type: UserResponseDto })
  async deactivateUser(@Param('id') id: string): Promise<User> {
    this.logger.log(`Deactivating user: ${id}`);
    return this.service.deactivateUser(id);
  }

  // Custom endpoint for finding user by email
  @Get('by-email/:email')
  @Roles('admin')
  @ApiOperation({ summary: 'Find user by email' })
  @ApiResponse({ status: 200, description: 'User found', type: UserResponseDto })
  async findByEmail(@Param('email') email: string): Promise<User | null> {
    this.logger.log(`Finding user by email: ${email}`);
    return this.service.findByEmail(email);
  }
}
```

## 🔒 **Security Implementation**

### **JWT Authentication Guard**

```typescript
// core/security/jwt-auth.guard.ts
import { Injectable, CanActivate, ExecutionContext, UnauthorizedException } from '@nestjs/common';
import { JwtService } from '@nestjs/jwt';
import { Request } from 'express';

@Injectable()
export class JwtAuthGuard implements CanActivate {
  constructor(private jwtService: JwtService) {}

  async canActivate(context: ExecutionContext): Promise<boolean> {
    const request = context.switchToHttp().getRequest<Request>();
    const token = this.extractTokenFromHeader(request);

    if (!token) {
      throw new UnauthorizedException('Access token is required');
    }

    try {
      const payload = await this.jwtService.verifyAsync(token, {
        secret: process.env.JWT_SECRET,
      });

      // Attach user to request for use in controllers
      request['user'] = payload;
      
      return true;
    } catch (error) {
      throw new UnauthorizedException('Invalid access token');
    }
  }

  private extractTokenFromHeader(request: Request): string | undefined {
    const [type, token] = request.headers.authorization?.split(' ') ?? [];
    return type === 'Bearer' ? token : undefined;
  }
}
```

### **Rate Limiting Guard**

```typescript
// core/security/rate-limit.guard.ts
import { Injectable, CanActivate, ExecutionContext, HttpException, HttpStatus } from '@nestjs/common';
import { RateLimiterMemory } from 'rate-limiter-flexible';

@Injectable()
export class RateLimitGuard implements CanActivate {
  private rateLimiter = new RateLimiterMemory({
    keyGenerator: (req: any) => req.ip || req.user?.id || 'anonymous',
    points: 100, // Number of requests
    duration: 60, // Per 60 seconds
  });

  async canActivate(context: ExecutionContext): Promise<boolean> {
    const request = context.switchToHttp().getRequest();
    
    try {
      await this.rateLimiter.consume(request.ip || request.user?.id || 'anonymous');
      return true;
    } catch (rejRes) {
      throw new HttpException(
        'Too many requests',
        HttpStatus.TOO_MANY_REQUESTS,
      );
    }
  }
}
```

## 📊 **Performance Monitoring**

### **Logging Interceptor**

```typescript
// core/monitoring/logging.interceptor.ts
import { Injectable, NestInterceptor, ExecutionContext, CallHandler, Logger } from '@nestjs/common';
import { Observable } from 'rxjs';
import { tap } from 'rxjs/operators';

@Injectable()
export class LoggingInterceptor implements NestInterceptor {
  private readonly logger = new Logger(LoggingInterceptor.name);

  intercept(context: ExecutionContext, next: CallHandler): Observable<any> {
    const request = context.switchToHttp().getRequest();
    const method = request.method;
    const url = request.url;
    const startTime = Date.now();

    this.logger.log(`Incoming request: ${method} ${url}`);

    return next.handle().pipe(
      tap(() => {
        const endTime = Date.now();
        const duration = endTime - startTime;
        
        this.logger.log(`Request completed: ${method} ${url} - ${duration}ms`);
        
        // Record metrics for monitoring
        this.recordMetrics(method, url, duration);
      }),
    );
  }

  private recordMetrics(method: string, url: string, duration: number) {
    // Integration with monitoring systems (Prometheus, DataDog, etc.)
    // metrics.recordHttpRequest(method, url, duration);
  }
}
```

### **Cache Interceptor**

```typescript
// core/cache/cache.interceptor.ts
import { Injectable, NestInterceptor, ExecutionContext, CallHandler } from '@nestjs/common';
import { Observable, of } from 'rxjs';
import { tap } from 'rxjs/operators';
import { ICacheService } from './cache-service.interface';

@Injectable()
export class CacheInterceptor implements NestInterceptor {
  constructor(private cacheService: ICacheService) {}

  async intercept(context: ExecutionContext, next: CallHandler): Promise<Observable<any>> {
    const request = context.switchToHttp().getRequest();
    
    // Only cache GET requests
    if (request.method !== 'GET') {
      return next.handle();
    }

    const cacheKey = this.generateCacheKey(request);
    const cachedResult = await this.cacheService.get(cacheKey);

    if (cachedResult) {
      return of(cachedResult);
    }

    return next.handle().pipe(
      tap(async (result) => {
        // Cache the result for 5 minutes
        await this.cacheService.set(cacheKey, result, 300);
      }),
    );
  }

  private generateCacheKey(request: any): string {
    const { url, query, user } = request;
    return `cache:${url}:${JSON.stringify(query)}:${user?.id || 'anonymous'}`;
  }
}
```

## 🧪 **Testing Implementation**

### **Unit Tests with SOLID Principles**

```typescript
// application/services/__tests__/users.service.spec.ts
import { Test, TestingModule } from '@nestjs/testing';
import { getRepositoryToken } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { UsersService } from '../users.service';
import { User } from '../../../domain/entities/user.entity';
import { ICacheService } from '../../../core/cache/cache-service.interface';
import { IEventBus } from '../../../core/events/event-bus.interface';
import { CreateUserDto } from '../../dto/user.dto';

describe('UsersService', () => {
  let service: UsersService;
  let repository: jest.Mocked<Repository<User>>;
  let cacheService: jest.Mocked<ICacheService>;
  let eventBus: jest.Mocked<IEventBus>;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        UsersService,
        {
          provide: getRepositoryToken(User),
          useValue: {
            findOne: jest.fn(),
            save: jest.fn(),
            create: jest.fn(),
            find: jest.fn(),
            createQueryBuilder: jest.fn(() => ({
              where: jest.fn().mockReturnThis(),
              getOne: jest.fn(),
              getMany: jest.fn(),
              getManyAndCount: jest.fn(),
            })),
          },
        },
        {
          provide: 'ICacheService',
          useValue: {
            get: jest.fn(),
            set: jest.fn(),
            delete: jest.fn(),
          },
        },
        {
          provide: 'IEventBus',
          useValue: {
            publish: jest.fn(),
          },
        },
      ],
    }).compile();

    service = module.get<UsersService>(UsersService);
    repository = module.get(getRepositoryToken(User));
    cacheService = module.get('ICacheService');
    eventBus = module.get('IEventBus');
  });

  describe('createOne', () => {
    it('should create a user following SOLID principles', async () => {
      // Arrange
      const createUserDto: CreateUserDto = {
        email: '<EMAIL>',
        firstName: 'John',
        lastName: 'Doe',
        password: 'password123',
      };

      const mockUser = User.create(
        createUserDto.email,
        createUserDto.firstName,
        createUserDto.lastName,
        'hashedPassword',
      );
      mockUser.id = 'uuid-123';

      repository.findOne.mockResolvedValue(null); // Email doesn't exist
      repository.save.mockResolvedValue(mockUser);
      cacheService.set.mockResolvedValue(undefined);
      eventBus.publish.mockResolvedValue(undefined);

      const mockRequest = {
        parsed: { paramsFilter: [], authPersist: {} },
        options: { routes: { createOneBase: { returnShallow: false } } },
      } as any;

      // Act
      const result = await service.createOne(mockRequest, createUserDto);

      // Assert
      expect(result).toBeDefined();
      expect(result.email).toBe(createUserDto.email);
      expect(repository.save).toHaveBeenCalled();
      expect(cacheService.set).toHaveBeenCalledWith(`user:${result.id}`, result, 3600);
      expect(eventBus.publish).toHaveBeenCalled();
    });

    it('should throw error for duplicate email (business rule)', async () => {
      // Arrange
      const createUserDto: CreateUserDto = {
        email: '<EMAIL>',
        firstName: 'John',
        lastName: 'Doe',
        password: 'password123',
      };

      const existingUser = new User();
      existingUser.email = createUserDto.email;

      repository.findOne.mockResolvedValue(existingUser);

      const mockRequest = {
        parsed: { paramsFilter: [], authPersist: {} },
        options: { routes: { createOneBase: { returnShallow: false } } },
      } as any;

      // Act & Assert
      await expect(service.createOne(mockRequest, createUserDto)).rejects.toThrow('Email already exists');
    });
  });

  describe('findByEmail', () => {
    it('should use cache for performance optimization', async () => {
      // Arrange
      const email = '<EMAIL>';
      const cachedUser = new User();
      cachedUser.email = email;

      cacheService.get.mockResolvedValue(cachedUser);

      // Act
      const result = await service.findByEmail(email);

      // Assert
      expect(result).toBe(cachedUser);
      expect(cacheService.get).toHaveBeenCalledWith(`user:email:${email}`);
      expect(repository.findOne).not.toHaveBeenCalled(); // Should not hit database
    });

    it('should fallback to database when cache miss', async () => {
      // Arrange
      const email = '<EMAIL>';
      const dbUser = new User();
      dbUser.email = email;

      cacheService.get.mockResolvedValue(null); // Cache miss
      repository.findOne.mockResolvedValue(dbUser);
      cacheService.set.mockResolvedValue(undefined);

      // Act
      const result = await service.findByEmail(email);

      // Assert
      expect(result).toBe(dbUser);
      expect(repository.findOne).toHaveBeenCalledWith({ where: { email } });
      expect(cacheService.set).toHaveBeenCalledWith(`user:email:${email}`, dbUser, 3600);
    });
  });
});
```

### **Integration Tests**

```typescript
// presentation/controllers/__tests__/users.controller.integration.spec.ts
import { Test, TestingModule } from '@nestjs/testing';
import { INestApplication } from '@nestjs/common';
import * as request from 'supertest';
import { AppModule } from '../../../app.module';
import { User } from '../../../domain/entities/user.entity';
import { getRepositoryToken } from '@nestjs/typeorm';
import { Repository } from 'typeorm';

describe('UsersController (Integration)', () => {
  let app: INestApplication;
  let userRepository: Repository<User>;

  beforeAll(async () => {
    const moduleFixture: TestingModule = await Test.createTestingModule({
      imports: [AppModule],
    }).compile();

    app = moduleFixture.createNestApplication();
    await app.init();

    userRepository = app.get<Repository<User>>(getRepositoryToken(User));
  });

  afterAll(async () => {
    await app.close();
  });

  beforeEach(async () => {
    // Clean database before each test
    await userRepository.clear();
  });

  describe('GET /users', () => {
    it('should return paginated users with proper performance', async () => {
      // Arrange: Create test data
      const users = Array.from({ length: 50 }, (_, i) => 
        User.create(`user${i}@example.com`, `First${i}`, `Last${i}`, 'password')
      );
      await userRepository.save(users);

      const startTime = Date.now();

      // Act
      const response = await request(app.getHttpServer())
        .get('/users')
        .query({
          limit: 10,
          offset: 0,
          sort: 'createdAt,DESC',
        })
        .expect(200);

      const endTime = Date.now();
      const executionTime = endTime - startTime;

      // Assert
      expect(response.body.data).toHaveLength(10);
      expect(response.body.total).toBe(50);
      expect(response.body.count).toBe(10);
      expect(executionTime).toBeLessThan(100); // Performance assertion
    });

    it('should handle complex filtering with proper algorithm complexity', async () => {
      // Arrange
      const users = [
        User.create('<EMAIL>', 'John', 'Doe', 'password'),
        User.create('<EMAIL>', 'Jane', 'Smith', 'password'),
        User.create('<EMAIL>', 'Bob', 'Johnson', 'password'),
      ];
      await userRepository.save(users);

      // Act
      const response = await request(app.getHttpServer())
        .get('/users')
        .query({
          filter: 'firstName||$cont||Jo',
          sort: 'firstName,ASC',
        })
        .expect(200);

      // Assert
      expect(response.body.data).toHaveLength(2); // John and Johnson
      expect(response.body.data[0].firstName).toBe('John');
    });
  });

  describe('POST /users', () => {
    it('should create user with proper validation and business rules', async () => {
      // Arrange
      const createUserDto = {
        email: '<EMAIL>',
        firstName: 'New',
        lastName: 'User',
        password: 'securePassword123',
      };

      // Act
      const response = await request(app.getHttpServer())
        .post('/users')
        .send(createUserDto)
        .expect(201);

      // Assert
      expect(response.body.email).toBe(createUserDto.email);
      expect(response.body.firstName).toBe(createUserDto.firstName);
      expect(response.body.password).toBeUndefined(); // Should be excluded
      expect(response.body.id).toBeDefined();
      expect(response.body.createdAt).toBeDefined();

      // Verify in database
      const savedUser = await userRepository.findOne({ 
        where: { email: createUserDto.email } 
      });
      expect(savedUser).toBeDefined();
      expect(savedUser.isActive).toBe(true);
    });

    it('should reject duplicate email (business rule validation)', async () => {
      // Arrange
      const existingUser = User.create('<EMAIL>', 'Existing', 'User', 'password');
      await userRepository.save(existingUser);

      const duplicateUserDto = {
        email: '<EMAIL>',
        firstName: 'Duplicate',
        lastName: 'User',
        password: 'password123',
      };

      // Act & Assert
      await request(app.getHttpServer())
        .post('/users')
        .send(duplicateUserDto)
        .expect(400);
    });
  });
});
```

## 🚀 **Deployment Configuration**

### **Docker Configuration**

```dockerfile
# Dockerfile
FROM node:18-alpine AS builder

WORKDIR /app

# Copy package files
COPY package*.json ./
COPY tsconfig*.json ./

# Install dependencies
RUN npm ci --only=production

# Copy source code
COPY src/ ./src/

# Build application
RUN npm run build

# Production stage
FROM node:18-alpine AS production

WORKDIR /app

# Create non-root user for security
RUN addgroup -g 1001 -S nodejs
RUN adduser -S nestjs -u 1001

# Copy built application
COPY --from=builder /app/dist ./dist
COPY --from=builder /app/node_modules ./node_modules
COPY --from=builder /app/package*.json ./

# Change ownership to non-root user
RUN chown -R nestjs:nodejs /app
USER nestjs

# Health check
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
  CMD curl -f http://localhost:3000/health || exit 1

EXPOSE 3000

CMD ["node", "dist/main"]
```

### **Docker Compose for Development**

```yaml
# docker-compose.yml
version: '3.8'

services:
  app:
    build: .
    ports:
      - "3000:3000"
    environment:
      - NODE_ENV=development
      - DATABASE_URL=********************************************/enterprise_crud
      - REDIS_URL=redis://redis:6379
      - JWT_SECRET=your-super-secret-jwt-key
    depends_on:
      - postgres
      - redis
    volumes:
      - ./src:/app/src
    command: npm run start:dev

  postgres:
    image: postgres:15-alpine
    environment:
      - POSTGRES_DB=enterprise_crud
      - POSTGRES_USER=postgres
      - POSTGRES_PASSWORD=password
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data

  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data

  prometheus:
    image: prom/prometheus
    ports:
      - "9090:9090"
    volumes:
      - ./monitoring/prometheus.yml:/etc/prometheus/prometheus.yml

  grafana:
    image: grafana/grafana
    ports:
      - "3001:3000"
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=admin
    volumes:
      - grafana_data:/var/lib/grafana

volumes:
  postgres_data:
  redis_data:
  grafana_data:
```

## 📊 **Performance Optimization**

### **Database Optimization**

```typescript
// core/database/database.config.ts
import { TypeOrmModuleOptions } from '@nestjs/typeorm';

export const databaseConfig: TypeOrmModuleOptions = {
  type: 'postgres',
  host: process.env.DB_HOST || 'localhost',
  port: parseInt(process.env.DB_PORT) || 5432,
  username: process.env.DB_USERNAME || 'postgres',
  password: process.env.DB_PASSWORD || 'password',
  database: process.env.DB_NAME || 'enterprise_crud',
  
  // Performance optimizations
  poolSize: 20,
  maxQueryExecutionTime: 1000,
  
  // Connection pooling
  extra: {
    connectionLimit: 20,
    acquireTimeout: 60000,
    timeout: 60000,
  },
  
  // Logging for development
  logging: process.env.NODE_ENV === 'development' ? ['query', 'error'] : ['error'],
  
  // Auto-load entities
  autoLoadEntities: true,
  
  // Synchronization (disable in production)
  synchronize: process.env.NODE_ENV === 'development',
  
  // Migrations
  migrations: ['dist/migrations/*.js'],
  migrationsRun: true,
};
```

### **Caching Strategy**

```typescript
// core/cache/redis-cache.service.ts
import { Injectable, Logger } from '@nestjs/common';
import { Redis } from 'ioredis';
import { ICacheService } from './cache-service.interface';

@Injectable()
export class RedisCacheService implements ICacheService {
  private readonly logger = new Logger(RedisCacheService.name);
  private readonly redis: Redis;

  constructor() {
    this.redis = new Redis({
      host: process.env.REDIS_HOST || 'localhost',
      port: parseInt(process.env.REDIS_PORT) || 6379,
      retryDelayOnFailover: 100,
      maxRetriesPerRequest: 3,
      lazyConnect: true,
    });

    this.redis.on('connect', () => {
      this.logger.log('Connected to Redis');
    });

    this.redis.on('error', (error) => {
      this.logger.error('Redis connection error:', error);
    });
  }

  async get<T>(key: string): Promise<T | null> {
    try {
      const value = await this.redis.get(key);
      return value ? JSON.parse(value) : null;
    } catch (error) {
      this.logger.error(`Cache get error for key ${key}:`, error);
      return null;
    }
  }

  async set<T>(key: string, value: T, ttl: number = 3600): Promise<void> {
    try {
      await this.redis.setex(key, ttl, JSON.stringify(value));
    } catch (error) {
      this.logger.error(`Cache set error for key ${key}:`, error);
    }
  }

  async delete(key: string): Promise<void> {
    try {
      await this.redis.del(key);
    } catch (error) {
      this.logger.error(`Cache delete error for key ${key}:`, error);
    }
  }

  async clear(): Promise<void> {
    try {
      await this.redis.flushall();
    } catch (error) {
      this.logger.error('Cache clear error:', error);
    }
  }

  // Advanced caching patterns
  async getOrSet<T>(
    key: string,
    factory: () => Promise<T>,
    ttl: number = 3600
  ): Promise<T> {
    let value = await this.get<T>(key);
    
    if (value === null) {
      value = await factory();
      await this.set(key, value, ttl);
    }
    
    return value;
  }

  // Cache invalidation patterns
  async invalidatePattern(pattern: string): Promise<void> {
    try {
      const keys = await this.redis.keys(pattern);
      if (keys.length > 0) {
        await this.redis.del(...keys);
      }
    } catch (error) {
      this.logger.error(`Cache invalidation error for pattern ${pattern}:`, error);
    }
  }
}
```

## 🎯 **Best Practices Summary**

### **1. Architecture Best Practices**
- ✅ **Clean Architecture**: Separate concerns across layers
- ✅ **SOLID Principles**: Apply consistently across all components
- ✅ **Design Patterns**: Use appropriate patterns for each scenario
- ✅ **Domain-Driven Design**: Model business logic in domain layer

### **2. Performance Best Practices**
- ✅ **Database Indexing**: Create proper indexes for query optimization
- ✅ **Caching Strategy**: Implement multi-level caching
- ✅ **Connection Pooling**: Optimize database connections
- ✅ **Query Optimization**: Use efficient algorithms and data structures

### **3. Security Best Practices**
- ✅ **Input Validation**: Validate all inputs using class-validator
- ✅ **SQL Injection Prevention**: Use parameterized queries
- ✅ **Authentication**: Implement JWT-based authentication
- ✅ **Rate Limiting**: Protect against abuse and DoS attacks

### **4. Testing Best Practices**
- ✅ **Unit Tests**: Test individual components in isolation
- ✅ **Integration Tests**: Test component interactions
- ✅ **E2E Tests**: Test complete user journeys
- ✅ **Performance Tests**: Validate performance requirements

### **5. Monitoring Best Practices**
- ✅ **Logging**: Comprehensive logging with structured format
- ✅ **Metrics**: Track performance and business metrics
- ✅ **Health Checks**: Monitor application health
- ✅ **Error Tracking**: Capture and analyze errors

This implementation guide provides a complete foundation for building enterprise-grade CRUD applications using NestJS-CRUD with proper architecture, design patterns, and best practices.